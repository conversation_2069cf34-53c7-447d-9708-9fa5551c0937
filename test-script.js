// 测试脚本 - 验证NodeSeek私信优化脚本的改进

// 模拟Utils对象
const Utils = {
    DEBUG: true,
    
    formatBackupFilename(date, userId) {
        const timestamp = date.toISOString().replace(/[:.]/g, '-').slice(0, -5) + '-' + Date.now().toString().slice(-6);
        return `nodeseek_chat_${userId}_${timestamp}`;
    },
    
    parseUTCToLocal(utcString) {
        return new Date(utcString).toLocaleString();
    },
    
    log(message, type = 'info') {
        const typeStr = typeof type === 'string' ? type.toUpperCase() : 'INFO';
        const timestamp = new Date().toLocaleString();
        console.log(`[NodeSeek私信优化] ${typeStr} [${timestamp}]: ${message}`);
    }
};

// 测试数据变化检测
class TestChatBackup {
    constructor() {
        this.lastDataHash = null;
        this.lastBackupTime = 0;
        this.minBackupInterval = 60000; // 1分钟
    }
    
    calculateDataHash(chats) {
        const dataString = JSON.stringify(chats.map(chat => ({
            member_id: chat.member_id,
            created_at: chat.created_at,
            msgCount: chat.msgArray ? chat.msgArray.length : 0
        })));
        
        // 简单的哈希函数
        let hash = 0;
        for (let i = 0; i < dataString.length; i++) {
            const char = dataString.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return hash.toString();
    }
    
    shouldPerformBackup(chats) {
        const currentTime = Date.now();
        const currentHash = this.calculateDataHash(chats);
        
        // 检查时间间隔
        if (currentTime - this.lastBackupTime < this.minBackupInterval) {
            Utils.log('备份间隔太短，跳过备份', 'debug');
            return false;
        }
        
        // 检查数据是否有变化
        if (this.lastDataHash === currentHash) {
            Utils.log('数据无变化，跳过备份', 'debug');
            return false;
        }
        
        Utils.log(`数据有变化，需要备份。旧哈希: ${this.lastDataHash}, 新哈希: ${currentHash}`, 'debug');
        return true;
    }
}

// 测试用例
function runTests() {
    console.log('=== NodeSeek私信优化脚本改进测试 ===\n');
    
    // 测试1: 备份文件名格式
    console.log('测试1: 备份文件名格式');
    const testDate = new Date('2025-07-31T14:33:38.000Z');
    const testUserId = 34093;
    const filename = Utils.formatBackupFilename(testDate, testUserId);
    console.log(`生成的文件名: ${filename}`);
    console.log(`是否包含用户ID: ${filename.includes(testUserId.toString())}`);
    console.log('✅ 测试通过\n');
    
    // 测试2: 时间本地化
    console.log('测试2: 时间本地化');
    const utcTime = '2025-07-31T14:33:38.000Z';
    const localTime = Utils.parseUTCToLocal(utcTime);
    console.log(`UTC时间: ${utcTime}`);
    console.log(`本地时间: ${localTime}`);
    console.log('✅ 测试通过\n');
    
    // 测试3: 数据变化检测
    console.log('测试3: 数据变化检测');
    const chatBackup = new TestChatBackup();
    
    const testChats1 = [
        { member_id: 1, created_at: '2025-07-31T14:33:38.000Z', msgArray: [1, 2, 3] },
        { member_id: 2, created_at: '2025-07-31T14:30:00.000Z', msgArray: [1, 2] }
    ];
    
    const testChats2 = [
        { member_id: 1, created_at: '2025-07-31T14:33:38.000Z', msgArray: [1, 2, 3] },
        { member_id: 2, created_at: '2025-07-31T14:30:00.000Z', msgArray: [1, 2] }
    ];
    
    const testChats3 = [
        { member_id: 1, created_at: '2025-07-31T14:33:38.000Z', msgArray: [1, 2, 3] },
        { member_id: 2, created_at: '2025-07-31T14:30:00.000Z', msgArray: [1, 2] },
        { member_id: 3, created_at: '2025-07-31T14:35:00.000Z', msgArray: [1] }
    ];
    
    // 第一次检查 - 应该需要备份（初始状态）
    console.log('第一次检查（初始状态）:');
    const shouldBackup1 = chatBackup.shouldPerformBackup(testChats1);
    console.log(`是否需要备份: ${shouldBackup1}`);
    
    if (shouldBackup1) {
        chatBackup.lastDataHash = chatBackup.calculateDataHash(testChats1);
        chatBackup.lastBackupTime = Date.now();
        console.log('模拟备份完成');
    }
    
    // 第二次检查 - 相同数据，应该不需要备份
    console.log('\n第二次检查（相同数据）:');
    const shouldBackup2 = chatBackup.shouldPerformBackup(testChats2);
    console.log(`是否需要备份: ${shouldBackup2}`);
    
    // 等待一段时间后，第三次检查 - 新数据，应该需要备份
    setTimeout(() => {
        console.log('\n第三次检查（新数据）:');
        chatBackup.lastBackupTime = Date.now() - 70000; // 模拟超过1分钟
        const shouldBackup3 = chatBackup.shouldPerformBackup(testChats3);
        console.log(`是否需要备份: ${shouldBackup3}`);
        console.log('✅ 测试通过\n');
        
        // 测试4: 日志时间戳
        console.log('测试4: 日志时间戳');
        Utils.log('这是一条测试日志消息', 'info');
        Utils.log('这是一条调试消息', 'debug');
        Utils.log('这是一条错误消息', 'error');
        console.log('✅ 测试通过\n');
        
        console.log('=== 所有测试完成 ===');
    }, 100);
}

// 运行测试
runTests();
